[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_close.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_close.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_upload.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_upload.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\raw_clottie.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\raw\\clottie.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_item_premium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\item_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_japan.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_japan.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_icon_uncheck.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_icon_uncheck.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_search_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\search_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_fragment_vip_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\fragment_vip_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_power.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_power.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_baseline_blur_circular_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\baseline_blur_circular_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_back.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_back.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_security.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_security.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_speed.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_speed.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_about.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_about.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_color_cursor.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\color_cursor.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_main_drawer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_main_drawer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_priority_badge_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\priority_badge_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_native_item_ads_container.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\native_item_ads_container.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_baseline_email_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\baseline_email_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_fragment_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\fragment_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_chevron_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_chevron_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\font_radiocan.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\font\\radiocan.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_fragment_all_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\fragment_all_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_bg_checkbox.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\bg_checkbox.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_no_ads.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_no_ads.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_share.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_share.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_upgrade.gif.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\upgrade.gif"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_round_corner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\round_corner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_upload.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\upload.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_protection_status_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\protection_status_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_dialog_custom_ad.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\dialog_custom_ad.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_category_badge_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\category_badge_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_unlimited.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_unlimited.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_input_bg_dark.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\input_bg_dark.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_autoref.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\autoref.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_native_ads_item_admob.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\native_ads_item_admob.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_pro_activity_premium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\pro_activity_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_admob_nativead_templete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\admob_nativead_templete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_circle_blue.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\circle_blue.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_arrow_down.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\arrow_down.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_btn_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\btn_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_vpnu.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\vpnu.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\anim_rotate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\anim\\rotate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_toolbar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\toolbar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_premium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_baseline_workspace_premium_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\baseline_workspace_premium_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_serverselect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\serverselect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_notification_empty.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_notification_empty.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\anim_stay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\anim\\stay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_fivegsmartvpn.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\fivegsmartvpn.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_rarrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_rarrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_splash_screen.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_splash_screen.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_baseline_list_alt_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\baseline_list_alt_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\anim_slide_in_left.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\anim\\slide_in_left.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_baseline_done_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\baseline_done_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_rate.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_rate.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_vpn_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_vpn_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_downarrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\downarrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_input_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\input_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_roundbg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\roundbg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_circlebagkground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\circlebagkground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_logo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_logo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_item_servers_premium.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\item_servers_premium.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_terms.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_terms.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_crown.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\crown.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\raw_loading.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\raw\\loading.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_content_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\content_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_notification_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_notification_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_servers.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_servers.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\raw_splashlottie.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\raw\\splashlottie.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_fragment_free_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\fragment_free_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_signal.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_signal.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_uparrow.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\uparrow.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_connected_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\connected_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_white_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\white_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_native_ad_item_facebook.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\native_ad_item_facebook.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_about_us.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\about_us.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_api_test.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_api_test.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_item_server.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\item_server.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_discount_badge.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\discount_badge.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_power.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\power.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_pro.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_pro.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_vpnhome.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\vpnhome.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_disconnected_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\disconnected_gradient.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_searchviewback.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\searchviewback.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_about_us.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_about_us.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_item_notification.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\item_notification.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_bg_list_native_ad.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\bg_list_native_ad.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_ic_icon_check.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\ic_icon_check.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-78:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-debug-77:\\drawable_connection_gradient.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.official.fivegfastvpn.app-main-79:\\drawable\\connection_gradient.xml"}]