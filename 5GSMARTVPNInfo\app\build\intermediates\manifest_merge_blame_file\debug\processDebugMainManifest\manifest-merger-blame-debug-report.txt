1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.official.fivegfastvpn"
4    android:versionCode="12"
5    android:versionName="12" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:5-67
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:4:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:5-79
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:5:22-76
13    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:5-77
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:6:22-74
14    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:5-87
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:7:22-84
15    <uses-permission android:name="com.android.vending.BILLING" />
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:5-67
15-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:8:22-64
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:5-77
16-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:9:22-74
17
18    <!-- VPN Service Permissions -->
19    <uses-permission android:name="android.permission.BIND_VPN_SERVICE" />
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:5-75
19-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:12:22-72
20    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:13:5-78
20-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:13:22-75
21    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:5-81
21-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:14:22-78
22
23    <queries>
23-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:12:5-19:15
24        <intent>
24-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:13:9-15:18
25            <action android:name="com.android.vending.billing.InAppBillingService.BIND" />
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:13-91
25-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:14:21-88
26        </intent>
27        <intent>
27-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:16:9-18:18
28            <action android:name="com.google.android.apps.play.billingtestcompanion.BillingOverrideService.BIND" />
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:13-116
28-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:17:21-113
29        </intent>
30
31        <package android:name="com.facebook.katana" /> <!-- For browser content -->
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:9-55
31-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:12:18-52
32        <intent>
32-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
33            <action android:name="android.intent.action.VIEW" />
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
33-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
34
35            <category android:name="android.intent.category.BROWSABLE" />
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
35-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
36
37            <data android:scheme="https" />
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
37-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
38        </intent> <!-- End of browser content -->
39        <!-- For CustomTabsService -->
40        <intent>
40-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
41-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
42        </intent> <!-- End of CustomTabsService -->
43        <!-- For MRAID capabilities -->
44        <intent>
44-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
45            <action android:name="android.intent.action.INSERT" />
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
45-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
46
47            <data android:mimeType="vnd.android.cursor.dir/event" />
47-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
48        </intent>
49        <intent>
49-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
50            <action android:name="android.intent.action.VIEW" />
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:13-65
50-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:39:21-62
51
52            <data android:scheme="sms" />
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
52-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:19-41
53        </intent>
54        <intent>
54-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
55            <action android:name="android.intent.action.DIAL" />
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
55-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
56
57            <data android:path="tel:" />
57-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:43:13-44
58        </intent>
59    </queries>
60
61    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
61-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
62    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
62-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
63    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:5-79
63-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:17:22-76
64    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
64-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:27:22-79
65    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
65-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:28:22-85
66    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_TOPICS" />
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
66-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
67
68    <permission
68-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
69        android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
69-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
70        android:protectionLevel="signature" />
70-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
71
72    <uses-permission android:name="com.official.fivegfastvpn.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
72-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
73
74    <application
74-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:15:5-93:15
75        android:name="com.official.fivegfastvpn.VPNApplication"
75-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:16:9-39
76        android:allowBackup="true"
76-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:17:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b5335a6eacc5065e32155bfa643f759a\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
78        android:dataExtractionRules="@xml/data_extraction_rules"
78-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:18:9-65
79        android:debuggable="true"
80        android:extractNativeLibs="true"
81        android:fullBackupContent="@xml/backup_rules"
81-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:19:9-54
82        android:icon="@mipmap/ic_launcher"
82-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:21:9-43
83        android:label="@string/app_name"
83-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:22:9-41
84        android:networkSecurityConfig="@xml/network_security_config"
84-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:20:9-69
85        android:roundIcon="@mipmap/ic_launcher_round"
85-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:23:9-54
86        android:supportsRtl="true"
86-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:24:9-35
87        android:testOnly="true"
88        android:theme="@style/Base.Theme._5GSMARTVPNInfo"
88-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:26:9-58
89        android:usesCleartextTraffic="true" >
89-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:25:9-44
90        <activity
90-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:28:9-30:40
91            android:name="com.official.fivegfastvpn.activity.About_Us"
91-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:29:13-46
92            android:exported="false" />
92-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:30:13-37
93        <activity
93-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:31:9-34:58
94            android:name="com.official.fivegfastvpn.activity.NotificationsActivity"
94-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:32:13-59
95            android:exported="false"
95-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:33:13-37
96            android:parentActivityName="com.official.fivegfastvpn.MainActivity" />
96-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:34:13-55
97        <activity android:name="com.official.fivegfastvpn.pro.PremiumActivity" />
97-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:9-57
97-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:37:19-54
98        <activity android:name="com.official.fivegfastvpn.MainActivity" />
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:38:9-50
98-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:38:19-47
99        <activity android:name="com.official.fivegfastvpn.activity.ServersActivity" />
99-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:9-62
99-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:39:19-59
100        <activity
100-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:40:9-48:20
101            android:name="com.official.fivegfastvpn.SplashActivity"
101-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:41:13-43
102            android:exported="true" >
102-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:42:13-36
103            <intent-filter>
103-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:43:13-47:29
104                <action android:name="android.intent.action.MAIN" />
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:44:17-69
104-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:44:25-66
105
106                <category android:name="android.intent.category.LAUNCHER" />
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:46:17-77
106-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:46:27-74
107            </intent-filter>
108        </activity>
109        <activity
109-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:49:9-54:52
110            android:name="de.blinkt.openvpn.DisconnectVPNActivity"
110-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:50:13-67
111            android:excludeFromRecents="true"
111-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:51:13-46
112            android:noHistory="true"
112-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:52:13-37
113            android:taskAffinity=".DisconnectVPN"
113-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:53:13-50
114            android:theme="@style/blinkt.dialog" />
114-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:54:13-49
115
116        <service
116-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:56:9-64:19
117            android:name="de.blinkt.openvpn.core.OpenVPNService"
117-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:57:13-65
118            android:exported="true"
118-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:58:13-36
119            android:foregroundServiceType="dataSync|dataSync"
119-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:59:13-53
120            android:permission="android.permission.BIND_VPN_SERVICE" >
120-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:60:13-69
121            <intent-filter>
121-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-63:29
122                <action android:name="android.net.VpnService" />
122-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:17-65
122-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:25-62
123            </intent-filter>
124        </service>
125        <service
125-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:65:9-71:19
126            android:name="com.official.fivegfastvpn.MyFirebaseMessagingService"
126-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:66:13-55
127            android:exported="false" >
127-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:67:13-37
128            <intent-filter>
128-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:68:13-70:29
129                <action android:name="com.google.firebase.MESSAGING_EVENT" />
129-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:69:17-78
129-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:69:25-75
130            </intent-filter>
131        </service>
132
133        <meta-data
133-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:73:9-75:70
134            android:name="com.google.android.gms.ads.APPLICATION_ID"
134-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:74:13-69
135            android:value="ca-app-pub-5193340328939721~2015388624" />
135-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:75:13-67
136        <meta-data
136-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:77:9-79:32
137            android:name="com.facebook.sdk.ApplicationId"
137-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:78:13-58
138            android:value="" />
138-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:79:13-29
139
140        <!-- Required: set your sentry.io project identifier (DSN) -->
141        <meta-data
141-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:5-159
142            android:name="io.sentry.dsn"
142-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:16-44
143            android:value="https://<EMAIL>/4508793236488192" />
143-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:82:45-156
144
145        <!-- enable automatic breadcrumbs for user interactions (clicks, swipes, scrolls) -->
146        <meta-data
146-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:5-95
147            android:name="io.sentry.traces.user-interaction.enable"
147-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:16-71
148            android:value="true" />
148-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:85:72-92
149        <!-- enable screenshot for crashes (could contain sensitive/PII data) -->
150        <meta-data
150-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:87:5-82
151            android:name="io.sentry.attach-screenshot"
151-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:87:16-58
152            android:value="true" />
152-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:87:59-79
153        <!-- enable view hierarchy for crashes -->
154        <meta-data
154-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:89:5-86
155            android:name="io.sentry.attach-view-hierarchy"
155-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:89:16-62
156            android:value="true" />
156-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:89:63-83
157
158        <!-- enable the performance API by setting a sample-rate, adjust in production env -->
159        <meta-data
159-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:5-82
160            android:name="io.sentry.traces.sample-rate"
160-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:16-59
161            android:value="1.0" />
161-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:92:60-79
162        <!-- VpnService-based OpenVPN implementation (V2) -->
163        <service
163-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-27:19
164            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
164-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-67
165            android:exported="true"
165-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
166            android:foregroundServiceType="dataSync"
166-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-53
167            android:permission="android.permission.BIND_VPN_SERVICE" >
167-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-69
168            <intent-filter>
168-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:61:13-63:29
169                <action android:name="android.net.VpnService" />
169-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:17-65
169-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:62:25-62
170            </intent-filter>
171        </service>
172
173        <meta-data
173-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:22:9-24:37
174            android:name="com.google.android.play.billingclient.version"
174-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:23:13-73
175            android:value="7.1.1" />
175-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:24:13-34
176
177        <activity
177-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:26:9-30:75
178            android:name="com.android.billingclient.api.ProxyBillingActivity"
178-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:27:13-78
179            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
179-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:28:13-96
180            android:exported="false"
180-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:29:13-37
181            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
181-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:30:13-72
182        <activity
182-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:31:9-35:75
183            android:name="com.android.billingclient.api.ProxyBillingActivityV2"
183-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:32:13-80
184            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
184-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:33:13-96
185            android:exported="false"
185-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:34:13-37
186            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
186-->[com.android.billingclient:billing:7.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\057ea422f11b2c1b5c1eb07523440294\transformed\billing-7.1.1\AndroidManifest.xml:35:13-72
187
188        <receiver
188-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
189            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
189-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
190            android:exported="true"
190-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
191            android:permission="com.google.android.c2dm.permission.SEND" >
191-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
192            <intent-filter>
192-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
193                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
193-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
193-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
194            </intent-filter>
195
196            <meta-data
196-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
197                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
197-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
198                android:value="true" />
198-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
199        </receiver>
200        <!--
201             FirebaseMessagingService performs security checks at runtime,
202             but set to not exported to explicitly avoid allowing another app to call it.
203        -->
204        <service
204-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
205            android:name="com.google.firebase.messaging.FirebaseMessagingService"
205-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
206            android:directBootAware="true"
206-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
207            android:exported="false" >
207-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
208            <intent-filter android:priority="-500" >
208-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:68:13-70:29
209                <action android:name="com.google.firebase.MESSAGING_EVENT" />
209-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:69:17-78
209-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\app\src\main\AndroidManifest.xml:69:25-75
210            </intent-filter>
211        </service>
212        <service
212-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
213            android:name="com.google.firebase.components.ComponentDiscoveryService"
213-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
214            android:directBootAware="true"
214-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
215            android:exported="false" >
215-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
216            <meta-data
216-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
217                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
217-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
218                android:value="com.google.firebase.components.ComponentRegistrar" />
218-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
219            <meta-data
219-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
220                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
220-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
221                android:value="com.google.firebase.components.ComponentRegistrar" />
221-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38f1e728606508438542533819a40e43\transformed\firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
222            <meta-data
222-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
223                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
223-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
224                android:value="com.google.firebase.components.ComponentRegistrar" />
224-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
225            <meta-data
225-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
226                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
226-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
227                android:value="com.google.firebase.components.ComponentRegistrar" />
227-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b370e0f8072109516ee30527c2197e56\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
228            <meta-data
228-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
229                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
229-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
230                android:value="com.google.firebase.components.ComponentRegistrar" />
230-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\63aaeacb2a36f7705d7d9290f3343c54\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
231            <meta-data
231-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
232                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
232-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
233                android:value="com.google.firebase.components.ComponentRegistrar" />
233-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
234            <meta-data
234-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
235                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
235-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
236                android:value="com.google.firebase.components.ComponentRegistrar" />
236-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4f02bec30df0defe4d8c6fe403d88b9f\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
237        </service>
238
239        <activity
239-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
240            android:name="com.google.android.gms.common.api.GoogleApiActivity"
240-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
241            android:exported="false"
241-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
242            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
242-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\abafdc52b4a83dcb3e4911636b323609\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
243        <activity
243-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:20:9-24:75
244            android:name="com.facebook.ads.AudienceNetworkActivity"
244-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:21:13-68
245            android:configChanges="keyboardHidden|orientation|screenSize|smallestScreenSize|screenLayout"
245-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:22:13-106
246            android:exported="false"
246-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:23:13-37
247            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
247-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:24:13-72
248
249        <provider
249-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:26:9-29:40
250            android:name="com.facebook.ads.AudienceNetworkContentProvider"
250-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:27:13-75
251            android:authorities="com.official.fivegfastvpn.AudienceNetworkContentProvider"
251-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:28:13-82
252            android:exported="false" /> <!-- Include the AdActivity and InAppPurchaseActivity configChanges and themes. -->
252-->[com.facebook.android:audience-network-sdk:6.20.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a5bf587049216d3118a6b6312c49a9ee\transformed\audience-network-sdk-6.20.0\AndroidManifest.xml:29:13-37
253        <activity
253-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
254            android:name="com.google.android.gms.ads.AdActivity"
254-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
255            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
255-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
256            android:exported="false"
256-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
257            android:theme="@android:style/Theme.Translucent" />
257-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
258
259        <provider
259-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
260            android:name="com.google.android.gms.ads.MobileAdsInitProvider"
260-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
261            android:authorities="com.official.fivegfastvpn.mobileadsinitprovider"
261-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
262            android:exported="false"
262-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
263            android:initOrder="100" />
263-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
264
265        <service
265-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
266            android:name="com.google.android.gms.ads.AdService"
266-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
267            android:enabled="true"
267-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
268            android:exported="false" />
268-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
269
270        <activity
270-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
271            android:name="com.google.android.gms.ads.OutOfContextTestingActivity"
271-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
272            android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|uiMode|screenSize|smallestScreenSize"
272-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
273            android:exported="false" />
273-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
274        <activity
274-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
275            android:name="com.google.android.gms.ads.NotificationHandlerActivity"
275-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
276            android:excludeFromRecents="true"
276-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
277            android:exported="false"
277-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
278            android:launchMode="singleTask"
278-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
279            android:taskAffinity=""
279-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
280            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
280-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
281
282        <meta-data
282-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
283            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
283-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
284            android:value="true" />
284-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
285        <meta-data
285-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
286            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
286-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
287            android:value="true" />
287-->[com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5210d35fff3ce47b7b7130b5da5c4686\transformed\play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
288
289        <provider
289-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
290            android:name="com.google.firebase.provider.FirebaseInitProvider"
290-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
291            android:authorities="com.official.fivegfastvpn.firebaseinitprovider"
291-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
292            android:directBootAware="true"
292-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
293            android:exported="false"
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
294            android:initOrder="100" />
294-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4b2f501c799de3f31c5b11b83b46cbb\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
295        <provider
295-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
296            android:name="androidx.startup.InitializationProvider"
296-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
297            android:authorities="com.official.fivegfastvpn.androidx-startup"
297-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
298            android:exported="false" >
298-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
299            <meta-data
299-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
300                android:name="androidx.work.WorkManagerInitializer"
300-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
301                android:value="androidx.startup" />
301-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
302            <meta-data
302-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
303                android:name="androidx.emoji2.text.EmojiCompatInitializer"
303-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
304                android:value="androidx.startup" />
304-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
305            <meta-data
305-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:29:13-31:52
306                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
306-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:30:17-78
307                android:value="androidx.startup" />
307-->[androidx.lifecycle:lifecycle-process:2.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cc925df91b3cd356b1aebb6c5f064f8\transformed\lifecycle-process-2.9.0\AndroidManifest.xml:31:17-49
308            <meta-data
308-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
309                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
309-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
310                android:value="androidx.startup" />
310-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
311        </provider>
312
313        <service
313-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
314            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
314-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
315            android:directBootAware="false"
315-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
316            android:enabled="@bool/enable_system_alarm_service_default"
316-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
317            android:exported="false" />
317-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
318        <service
318-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
319            android:name="androidx.work.impl.background.systemjob.SystemJobService"
319-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
320            android:directBootAware="false"
320-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
321            android:enabled="@bool/enable_system_job_service_default"
321-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
322            android:exported="true"
322-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
323            android:permission="android.permission.BIND_JOB_SERVICE" />
323-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
324        <service
324-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
325            android:name="androidx.work.impl.foreground.SystemForegroundService"
325-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
326            android:directBootAware="false"
326-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
327            android:enabled="@bool/enable_system_foreground_service_default"
327-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
328            android:exported="false" />
328-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
329
330        <receiver
330-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
331            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
331-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
332            android:directBootAware="false"
332-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
333            android:enabled="true"
333-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
334            android:exported="false" />
334-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
335        <receiver
335-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
336            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
336-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
337            android:directBootAware="false"
337-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
338            android:enabled="false"
338-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
339            android:exported="false" >
339-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
340            <intent-filter>
340-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
341                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
341-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
342                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
342-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
343            </intent-filter>
344        </receiver>
345        <receiver
345-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
346            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
346-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
347            android:directBootAware="false"
347-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
348            android:enabled="false"
348-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
349            android:exported="false" >
349-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
350            <intent-filter>
350-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
351                <action android:name="android.intent.action.BATTERY_OKAY" />
351-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
351-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
352                <action android:name="android.intent.action.BATTERY_LOW" />
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
352-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
353            </intent-filter>
354        </receiver>
355        <receiver
355-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
356            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
356-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
357            android:directBootAware="false"
357-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
358            android:enabled="false"
358-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
359            android:exported="false" >
359-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
360            <intent-filter>
360-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
361                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
361-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
361-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
362                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
362-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
363            </intent-filter>
364        </receiver>
365        <receiver
365-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
366            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
366-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
367            android:directBootAware="false"
367-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
368            android:enabled="false"
368-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
369            android:exported="false" >
369-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
370            <intent-filter>
370-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
371                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
371-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
372            </intent-filter>
373        </receiver>
374        <receiver
374-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
375            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
375-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
376            android:directBootAware="false"
376-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
377            android:enabled="false"
377-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
378            android:exported="false" >
378-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
379            <intent-filter>
379-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
380                <action android:name="android.intent.action.BOOT_COMPLETED" />
380-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
380-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
381                <action android:name="android.intent.action.TIME_SET" />
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
381-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
382                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
382-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
383            </intent-filter>
384        </receiver>
385        <receiver
385-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
386            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
386-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
387            android:directBootAware="false"
387-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
388            android:enabled="@bool/enable_system_alarm_service_default"
388-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
389            android:exported="false" >
389-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
390            <intent-filter>
390-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
391                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
391-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
392            </intent-filter>
393        </receiver>
394        <receiver
394-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
395            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
395-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
396            android:directBootAware="false"
396-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
397            android:enabled="true"
397-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
398            android:exported="true"
398-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
399            android:permission="android.permission.DUMP" >
399-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
400            <intent-filter>
400-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
401                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
401-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
401-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
402            </intent-filter>
403        </receiver>
404
405        <service
405-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
406            android:name="androidx.room.MultiInstanceInvalidationService"
406-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
407            android:directBootAware="true"
407-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
408            android:exported="false" />
408-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
409
410        <uses-library
410-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
411            android:name="androidx.window.extensions"
411-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
412            android:required="false" />
412-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
413        <uses-library
413-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
414            android:name="androidx.window.sidecar"
414-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
415            android:required="false" /> <!-- 'android:authorities' must be unique in the device, across all apps -->
415-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7cf4f5caab9a5df674a7f293958b043f\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
416        <provider
416-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:12:9-15:40
417            android:name="io.sentry.android.core.SentryInitProvider"
417-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:13:13-69
418            android:authorities="com.official.fivegfastvpn.SentryInitProvider"
418-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:14:13-70
419            android:exported="false" />
419-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:15:13-37
420        <provider
420-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:16:9-20:39
421            android:name="io.sentry.android.core.SentryPerformanceProvider"
421-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:17:13-76
422            android:authorities="com.official.fivegfastvpn.SentryPerformanceProvider"
422-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:18:13-77
423            android:exported="false"
423-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:19:13-37
424            android:initOrder="200" />
424-->[io.sentry:sentry-android-core:8.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab9fe2cfea1556cdf59ed28c539ad42\transformed\sentry-android-core-8.1.0\AndroidManifest.xml:20:13-36
425
426        <uses-library
426-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
427            android:name="android.ext.adservices"
427-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
428            android:required="false" />
428-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\04acc53f658e9c1c6ef85818b36fbc26\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
429
430        <meta-data
430-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
431            android:name="com.google.android.gms.version"
431-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
432            android:value="@integer/google_play_services_version" />
432-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1aae7574d5734885cc339c3642ba94ef\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
433
434        <receiver
434-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
435            android:name="androidx.profileinstaller.ProfileInstallReceiver"
435-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
436            android:directBootAware="false"
436-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
437            android:enabled="true"
437-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
438            android:exported="true"
438-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
439            android:permission="android.permission.DUMP" >
439-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
440            <intent-filter>
440-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
441                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
441-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
442            </intent-filter>
443            <intent-filter>
443-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
444                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
444-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
445            </intent-filter>
446            <intent-filter>
446-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
447                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
447-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
448            </intent-filter>
449            <intent-filter>
449-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
450                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
450-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4d8601877e3451e78513d552b27b584\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
451            </intent-filter>
452        </receiver>
453
454        <service
454-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
455            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
455-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
456            android:exported="false" >
456-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
457            <meta-data
457-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
458                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
458-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
459                android:value="cct" />
459-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f52e0b63325323d499cb62a020bb0cf\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
460        </service>
461        <service
461-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
462            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
462-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
463            android:exported="false"
463-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
464            android:permission="android.permission.BIND_JOB_SERVICE" >
464-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
465        </service>
466
467        <receiver
467-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
468            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
468-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
469            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
469-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41184f8ba20d1afc615f0577f56d25b7\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
470        <activity
470-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
471            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
471-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
472            android:exported="false"
472-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
473            android:stateNotNeeded="true"
473-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
474            android:theme="@style/Theme.PlayCore.Transparent" />
474-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9e98fd6bc8fdf57163ff3533ceec6a5b\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
475    </application>
476
477</manifest>
